"""
TruthFinder API integration for people search functionality.
Replaces web scraping with reliable API calls.
"""

import requests
import logging
from typing import List, Dict, Optional
from datetime import datetime

from ..core import ScrapingResult


class TruthFinderAPI:
    """TruthFinder API client for people search."""
    
    def __init__(self, api_key: str, app_id: str = "tf-web"):
        self.api_key = api_key
        self.app_id = app_id
        self.base_url = "https://api.truthfinder.com"
        self.logger = logging.getLogger(__name__)
        
        self.headers = {
            'api-key': self.api_key,
            'app-id': self.app_id,
            'cache-control': 'no-cache',
            'content-type': 'application/json',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
    
    def search_business_owners(self, business_type: str, location: str) -> List[ScrapingResult]:
        """Search for business owners using TruthFinder API."""
        try:
            # Parse location
            location_parts = location.split()
            city = " ".join(location_parts[:-1])
            state = location_parts[-1] if location_parts else ""
            
            # Search for people in the location with business associations
            search_params = {
                "city": city,
                "state": state,
                "business_type": business_type,
                "include_business_info": True
            }
            
            response = requests.post(
                f"{self.base_url}/search",
                headers=self.headers,
                json=search_params,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                return self._parse_api_response(data, business_type, location)
            else:
                self.logger.error(f"API request failed: {response.status_code}")
                return []
                
        except Exception as e:
            self.logger.error(f"TruthFinder API error: {e}")
            return []
    
    def _parse_api_response(self, data: List[Dict], business_type: str, location: str) -> List[ScrapingResult]:
        """Parse TruthFinder API response into ScrapingResult objects."""
        results = []
        
        for person_data in data:
            # Extract names
            names = person_data.get('names', [])
            if not names:
                continue
            
            primary_name = names[0]
            first_name = primary_name.get('first', '')
            middle_name = primary_name.get('middle', '')
            last_name = primary_name.get('last', '')
            
            full_name = f"{first_name} {middle_name} {last_name}".strip()
            
            # Extract locations
            locations = person_data.get('locations', [])
            address_info = None
            if locations:
                address_data = locations[0].get('address', {})
                street = address_data.get('street', '').replace('*', '')
                city = address_data.get('city', '')
                state = address_data.get('state', '')
                zip_code = address_data.get('zip_code', '').replace('*', '')
                
                if street and city and state:
                    address_info = f"{street}, {city}, {state} {zip_code}".strip()
            
            # Create result
            result = ScrapingResult(
                owner_name=full_name,
                business_name=f"{first_name}'s {business_type.title()} Business",  # Inferred
                business_type=business_type,
                location=location,
                source="truthfinder_api",
                address=address_info,
                scraped_at=datetime.now(),
                raw_data={
                    'api_response': person_data,
                    'data_quality': 'high',
                    'confidence_score': 0.9,
                    'api_source': 'truthfinder'
                }
            )
            
            results.append(result)
        
        return results
    
    def get_person_details(self, person_id: str) -> Optional[Dict]:
        """Get detailed information for a specific person."""
        try:
            response = requests.get(
                f"{self.base_url}/person/{person_id}",
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                self.logger.error(f"Person details request failed: {response.status_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error getting person details: {e}")
            return None
