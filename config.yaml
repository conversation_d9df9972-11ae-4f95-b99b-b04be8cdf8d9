# Business Owner Scraper Configuration

# General Settings
general:
  max_workers: 5
  request_delay: 2 # seconds between requests
  timeout: 30
  max_retries: 3
  output_format: "csv" # csv, xlsx, json
  output_directory: "./results"

# Anti-Bot Protection Settings
anti_bot:
  use_proxies: true
  rotate_user_agents: true
  use_cloudscraper: true
  use_undetected_chrome: true
  headless: true

# Proxy Settings (optional - can be left empty)
proxies:
  http_proxies: []
  # Example:
  # - "http://username:<EMAIL>:8080"
  # - "http://username:<EMAIL>:8080"

  socks_proxies: []
  # Example:
  # - "socks5://username:<EMAIL>:1080"

# Data Sources Configuration
# All sources now use Google search with site: operator as specified by client
sources:
  bbb:
    enabled: true
    base_url: "https://www.bbb.org"
    search_pattern: 'site:bbb.org "Owner" "{business_type}" "{location}"'
    google_search: true
    max_pages: 5

  manta:
    enabled: true
    base_url: "https://www.manta.com"
    search_pattern: 'site:manta.com "Owner" "{business_type}" "{location}"'
    google_search: true
    max_pages: 5

  linkedin:
    enabled: true
    base_url: "https://www.linkedin.com"
    search_pattern: 'site:linkedin.com "Owner" "{business_type}" "{location}"'
    google_search: true
    max_pages: 3

  truepeoplesearch:
    enabled: true
    base_url: "https://www.truepeoplesearch.com"
    search_pattern: 'site:truepeoplesearch.com "Owner" {business_type} {location}'
    max_pages: 3
    cloudflare_protected: true

  cyberbackgroundchecks:
    enabled: true
    base_url: "https://www.cyberbackgroundchecks.com"
    search_pattern: 'site:cyberbackgroundchecks.com "Owner" {business_type} {location}'
    max_pages: 3
    cloudflare_protected: true

# Search Configuration
search:
  default_business_types:
    - "lawn care"
    - "restaurant"
    - "construction"
    - "plumbing"
    - "electrical"

  default_locations:
    - "dallas"
    - "houston"
    - "austin"
    - "san antonio"

# Data Extraction Patterns
extraction:
  owner_patterns:
    - "Owner: ([A-Za-z\\s]+)"
    - "CEO: ([A-Za-z\\s]+)"
    - "President: ([A-Za-z\\s]+)"
    - "Founder: ([A-Za-z\\s]+)"
    - "Managing Director: ([A-Za-z\\s]+)"

  business_patterns:
    - "Company: ([A-Za-z0-9\\s&,.-]+)"
    - "Business: ([A-Za-z0-9\\s&,.-]+)"
    - "Organization: ([A-Za-z0-9\\s&,.-]+)"

# Output Configuration
output:
  csv_columns:
    - "owner_name"
    - "business_name"
    - "business_type"
    - "location"
    - "source"
    - "url"
    - "phone"
    - "email"
    - "address"
    - "scraped_at"

  deduplication:
    enabled: true
    similarity_threshold: 0.8
    key_fields: ["owner_name", "business_name"]
