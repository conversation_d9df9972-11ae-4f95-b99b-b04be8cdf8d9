"""
Anti-bot protection utilities for bypassing various detection mechanisms.
"""

import random
import time
import json
import base64
from typing import Dict, List, Optional, Tuple
from urllib.parse import urljoin, urlparse
import logging

import requests
import cloudscraper
from fake_useragent import UserAgent
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, WebDriverException
import undetected_chromedriver as uc


class AntiBot:
    """Anti-bot protection bypass utilities."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.user_agent = UserAgent()
        
        # Common headers for different browsers
        self.browser_headers = {
            'chrome': {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'en-US,en;q=0.9',
                'Cache-Control': 'max-age=0',
                'Sec-Ch-Ua': '"Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"Windows"',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1'
            },
            'firefox': {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Encoding': 'gzip, deflate',
                'Accept-Language': 'en-US,en;q=0.5',
                'Cache-Control': 'max-age=0',
                'Upgrade-Insecure-Requests': '1'
            }
        }
    
    def get_random_headers(self, browser: str = 'chrome') -> Dict[str, str]:
        """Get random headers for the specified browser."""
        headers = self.browser_headers.get(browser, self.browser_headers['chrome']).copy()
        headers['User-Agent'] = self.get_random_user_agent()
        return headers
    
    def get_random_user_agent(self) -> str:
        """Get a random user agent string."""
        try:
            return self.user_agent.random
        except Exception:
            # Fallback user agents
            fallback_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/119.0'
            ]
            return random.choice(fallback_agents)
    
    def create_cloudscraper_session(self, **kwargs) -> cloudscraper.CloudScraper:
        """Create a CloudScraper session with anti-detection."""
        scraper = cloudscraper.create_scraper(
            browser={
                'browser': 'chrome',
                'platform': 'windows',
                'mobile': False
            },
            **kwargs
        )
        
        # Update headers
        scraper.headers.update(self.get_random_headers())
        
        return scraper
    
    def create_undetected_driver(self, headless: bool = True, 
                                proxy: Optional[str] = None) -> Optional[webdriver.Chrome]:
        """Create an undetected Chrome driver."""
        options = uc.ChromeOptions()
        
        if headless:
            options.add_argument('--headless=new')
        
        # Anti-detection arguments
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins')
        options.add_argument('--disable-images')
        options.add_argument('--disable-javascript')
        options.add_argument('--disable-gpu')
        options.add_argument('--no-first-run')
        options.add_argument('--no-default-browser-check')
        options.add_argument('--disable-default-apps')
        
        # Set user agent
        options.add_argument(f'--user-agent={self.get_random_user_agent()}')
        
        # Proxy configuration
        if proxy:
            options.add_argument(f'--proxy-server={proxy}')
        
        # Window size randomization
        width = random.randint(1200, 1920)
        height = random.randint(800, 1080)
        options.add_argument(f'--window-size={width},{height}')
        
        try:
            driver = uc.Chrome(options=options)
            
            # Execute stealth scripts
            self._apply_stealth_scripts(driver)
            
            return driver
        except Exception as e:
            self.logger.error(f"Failed to create undetected driver: {e}")
            return None
    
    def _apply_stealth_scripts(self, driver: webdriver.Chrome):
        """Apply stealth scripts to hide automation."""
        stealth_scripts = [
            # Remove webdriver property
            "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})",
            
            # Override plugins
            """
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5]
            });
            """,
            
            # Override languages
            """
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en']
            });
            """,
            
            # Override permissions
            """
            const originalQuery = window.navigator.permissions.query;
            return window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
            """
        ]
        
        for script in stealth_scripts:
            try:
                driver.execute_script(script)
            except Exception as e:
                self.logger.warning(f"Failed to execute stealth script: {e}")
    
    def bypass_cloudflare(self, url: str, max_retries: int = 3) -> Optional[requests.Response]:
        """Bypass Cloudflare protection using CloudScraper."""
        scraper = self.create_cloudscraper_session()
        
        for attempt in range(max_retries):
            try:
                self.logger.info(f"Attempting Cloudflare bypass for {url} (attempt {attempt + 1})")
                
                # Random delay
                time.sleep(random.uniform(2, 5))
                
                response = scraper.get(url, timeout=30)
                
                # Check if we got past Cloudflare
                if self._is_cloudflare_challenge(response.text):
                    self.logger.warning(f"Still blocked by Cloudflare on attempt {attempt + 1}")
                    time.sleep(random.uniform(5, 10))
                    continue
                
                self.logger.info(f"Successfully bypassed Cloudflare for {url}")
                return response
                
            except Exception as e:
                self.logger.error(f"Cloudflare bypass attempt {attempt + 1} failed: {e}")
                time.sleep(random.uniform(3, 7))
        
        self.logger.error(f"Failed to bypass Cloudflare for {url} after {max_retries} attempts")
        return None
    
    def _is_cloudflare_challenge(self, html: str) -> bool:
        """Check if the response contains a Cloudflare challenge."""
        cloudflare_indicators = [
            'Checking your browser before accessing',
            'DDoS protection by Cloudflare',
            'cf-browser-verification',
            'cf-challenge-form',
            'cloudflare-static',
            'cf-ray'
        ]
        
        html_lower = html.lower()
        return any(indicator.lower() in html_lower for indicator in cloudflare_indicators)
    
    def selenium_cloudflare_bypass(self, url: str, max_wait: int = 30) -> Optional[str]:
        """Use Selenium to bypass Cloudflare challenges."""
        driver = self.create_undetected_driver(headless=False)  # Non-headless for CF
        
        if not driver:
            return None
        
        try:
            self.logger.info(f"Using Selenium to bypass Cloudflare for {url}")
            driver.get(url)
            
            # Wait for Cloudflare challenge to complete
            wait = WebDriverWait(driver, max_wait)
            
            # Wait until we're past the challenge page
            def not_cloudflare_challenge(driver):
                return not self._is_cloudflare_challenge(driver.page_source)
            
            wait.until(not_cloudflare_challenge)
            
            # Additional wait for page to fully load
            time.sleep(random.uniform(3, 6))
            
            page_source = driver.page_source
            self.logger.info(f"Successfully bypassed Cloudflare with Selenium for {url}")
            
            return page_source
            
        except TimeoutException:
            self.logger.error(f"Timeout waiting for Cloudflare bypass on {url}")
            return None
        except Exception as e:
            self.logger.error(f"Selenium Cloudflare bypass failed for {url}: {e}")
            return None
        finally:
            try:
                driver.quit()
            except Exception:
                pass
    
    def random_delay(self, min_delay: float = 1.0, max_delay: float = 3.0):
        """Add random delay between requests."""
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
    
    def get_proxy_list(self) -> List[str]:
        """Get list of configured proxies."""
        proxies = []
        
        http_proxies = self.config.get('proxies', {}).get('http_proxies', [])
        socks_proxies = self.config.get('proxies', {}).get('socks_proxies', [])
        
        proxies.extend(http_proxies)
        proxies.extend(socks_proxies)
        
        return proxies
    
    def get_random_proxy(self) -> Optional[str]:
        """Get a random proxy from the configured list."""
        proxies = self.get_proxy_list()
        return random.choice(proxies) if proxies else None
    
    def test_proxy(self, proxy: str, test_url: str = "http://httpbin.org/ip") -> bool:
        """Test if a proxy is working."""
        try:
            proxies = {'http': proxy, 'https': proxy}
            response = requests.get(test_url, proxies=proxies, timeout=10)
            return response.status_code == 200
        except Exception:
            return False
