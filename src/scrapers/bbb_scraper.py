"""
Better Business Bureau (BBB.org) scraper for business owner information.
"""

import re
from typing import List
from urllib.parse import urljoin, quote_plus
import requests

from bs4 import BeautifulSoup

from .base_scraper import BaseScraper
from ..core import ScrapingResult


class BBBScraper(BaseScraper):
    """Scraper for BBB.org business listings."""
    
    def __init__(self, engine):
        super().__init__(engine, 'bbb')
    
    def search(self, business_type: str, location: str) -> List[ScrapingResult]:
        """Search BBB for business owners."""
        if not self.is_enabled():
            return []
        
        self.logger.info(f"Searching BBB for '{business_type}' in '{location}'")
        
        results = []
        search_url = self.build_search_url(business_type, location)
        listing_urls = self.get_search_results_urls(search_url)
        
        self.logger.info(f"Found {len(listing_urls)} BBB listings to scrape")
        
        for url in listing_urls:
            listing_results = self.scrape_listing(url, business_type, location)
            results.extend(listing_results)
        
        return results
    
    def build_search_url(self, business_type: str, location: str) -> str:
        """Build BBB search URL."""
        # BBB search format: https://www.bbb.org/search?find_country=USA&find_text=lawn+care&find_loc=dallas+tx
        query = f"{business_type} owner"
        encoded_query = quote_plus(query)
        encoded_location = quote_plus(location)
        
        return f"{self.base_url}/search?find_country=USA&find_text={encoded_query}&find_loc={encoded_location}"
    
    def _build_page_url(self, base_url: str, page: int) -> str:
        """Build URL for specific page."""
        if page == 1:
            return base_url
        return f"{base_url}&page={page}"
    
    def _extract_listing_urls(self, soup: BeautifulSoup) -> List[str]:
        """Extract business listing URLs from BBB search results."""
        urls = []
        
        # BBB listing selectors
        selectors = [
            'a[href*="/us/"]',  # BBB business profile URLs
            '.search-result-title a',
            '.business-name a',
            'h3 a[href*="/us/"]'
        ]
        
        for selector in selectors:
            links = soup.select(selector)
            for link in links:
                href = link.get('href')
                if href:
                    # Convert relative URLs to absolute
                    if href.startswith('/'):
                        href = urljoin(self.base_url, href)
                    
                    # Filter for business profile URLs
                    if '/us/' in href and 'bbb.org' in href:
                        urls.append(href)
        
        return urls
    
    def extract_owner_info(self, soup: BeautifulSoup, url: str) -> List[ScrapingResult]:
        """Extract owner information from BBB business page."""
        results = []
        
        # Extract business name
        business_name = self._extract_business_name(soup)
        
        # Extract owner information using various patterns
        owner_names = self._extract_owner_names(soup)
        
        if owner_names:
            for owner_name in owner_names:
                result = ScrapingResult(
                    owner_name=self.clean_text(owner_name),
                    business_name=self.clean_text(business_name),
                    source=self.source_name,
                    url=url
                )
                results.append(result)
        else:
            # Create result with business info even if no owner found
            result = ScrapingResult(
                business_name=self.clean_text(business_name),
                source=self.source_name,
                url=url
            )
            results.append(result)
        
        return results
    
    def _extract_business_name(self, soup: BeautifulSoup) -> str:
        """Extract business name from BBB page."""
        selectors = [
            'h1.business-name',
            'h1[data-testid="business-name"]',
            '.business-header h1',
            'h1.biz-name',
            '.business-title h1'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text(strip=True)
        
        # Fallback to title tag
        title = soup.find('title')
        if title:
            title_text = title.get_text()
            # Remove BBB suffix
            title_text = re.sub(r'\s*\|\s*Better Business Bureau.*$', '', title_text)
            return title_text.strip()
        
        return ""
    
    def _extract_owner_names(self, soup: BeautifulSoup) -> List[str]:
        """Extract owner names from BBB page."""
        owner_names = []
        
        # Get all text content
        page_text = soup.get_text()
        
        # Owner extraction patterns
        patterns = [
            r'Owner[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'Principal[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'CEO[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'President[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'Founder[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'Managing Director[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'Contact Person[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            owner_names.extend(matches)
        
        # Look for owner info in specific sections
        owner_sections = soup.select('.business-details, .contact-info, .business-info')
        for section in owner_sections:
            section_text = section.get_text()
            for pattern in patterns:
                matches = re.findall(pattern, section_text, re.IGNORECASE)
                owner_names.extend(matches)
        
        # Look for structured data
        structured_data = soup.find_all('script', type='application/ld+json')
        for script in structured_data:
            try:
                import json
                data = json.loads(script.string)
                if isinstance(data, dict):
                    # Look for person or founder information
                    if 'founder' in data:
                        founder = data['founder']
                        if isinstance(founder, dict) and 'name' in founder:
                            owner_names.append(founder['name'])
                        elif isinstance(founder, str):
                            owner_names.append(founder)
            except:
                continue
        
        # Clean and deduplicate
        cleaned_names = []
        for name in owner_names:
            cleaned_name = self.clean_text(name)
            if cleaned_name and len(cleaned_name) > 2 and cleaned_name not in cleaned_names:
                # Basic validation - should be a proper name
                if re.match(r'^[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*$', cleaned_name):
                    cleaned_names.append(cleaned_name)
        
        return cleaned_names
