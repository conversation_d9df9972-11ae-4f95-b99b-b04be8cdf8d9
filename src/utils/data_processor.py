"""
Data processing and deduplication utilities for scraped business owner information.
"""

import re
import logging
from typing import List, Dict, Set, Tuple, Optional
from difflib import SequenceMatcher
from collections import defaultdict
import pandas as pd
from datetime import datetime

from ..core import ScrapingResult


class DataProcessor:
    """Data processing and deduplication for scraped results."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.dedup_config = config.get('output', {}).get('deduplication', {})
        self.similarity_threshold = self.dedup_config.get('similarity_threshold', 0.8)
        self.key_fields = self.dedup_config.get('key_fields', ['owner_name', 'business_name'])
    
    def process_results(self, results: List[ScrapingResult]) -> List[ScrapingResult]:
        """Process and clean scraped results."""
        self.logger.info(f"Processing {len(results)} scraped results")
        
        # Step 1: Clean and standardize data
        cleaned_results = self._clean_results(results)
        
        # Step 2: Validate results
        valid_results = self._validate_results(cleaned_results)
        
        # Step 3: Deduplicate results
        if self.dedup_config.get('enabled', True):
            deduplicated_results = self._deduplicate_results(valid_results)
        else:
            deduplicated_results = valid_results
        
        # Step 4: Enrich results
        enriched_results = self._enrich_results(deduplicated_results)
        
        self.logger.info(f"Processing complete: {len(enriched_results)} final results")
        
        return enriched_results
    
    def _clean_results(self, results: List[ScrapingResult]) -> List[ScrapingResult]:
        """Clean and standardize result data."""
        cleaned_results = []
        
        for result in results:
            # Clean owner name
            if result.owner_name:
                result.owner_name = self._clean_name(result.owner_name)
            
            # Clean business name
            if result.business_name:
                result.business_name = self._clean_business_name(result.business_name)
            
            # Clean location
            if result.location:
                result.location = self._clean_location(result.location)
            
            # Clean contact information
            if result.phone:
                result.phone = self._clean_phone(result.phone)
            
            if result.email:
                result.email = self._clean_email(result.email)
            
            if result.address:
                result.address = self._clean_address(result.address)
            
            cleaned_results.append(result)
        
        return cleaned_results
    
    def _clean_name(self, name: str) -> str:
        """Clean and standardize person names."""
        if not name:
            return ""
        
        # Remove extra whitespace
        name = re.sub(r'\s+', ' ', name.strip())
        
        # Remove common prefixes/suffixes
        name = re.sub(r'^(Mr\.|Mrs\.|Ms\.|Dr\.|Prof\.)\s*', '', name, flags=re.IGNORECASE)
        name = re.sub(r'\s*(Jr\.|Sr\.|III|IV|PhD|MD)$', '', name, flags=re.IGNORECASE)
        
        # Remove business titles
        name = re.sub(r'\s*(Owner|CEO|President|Founder|Manager)$', '', name, flags=re.IGNORECASE)
        
        # Title case
        name = name.title()
        
        # Fix common issues
        name = re.sub(r'\bMc([a-z])', r'Mc\1', name)  # McDonald -> McDonald
        name = re.sub(r'\bO\'([a-z])', r"O'\1", name)  # O'connor -> O'Connor
        
        return name.strip()
    
    def _clean_business_name(self, business_name: str) -> str:
        """Clean and standardize business names."""
        if not business_name:
            return ""
        
        # Remove extra whitespace
        business_name = re.sub(r'\s+', ' ', business_name.strip())
        
        # Remove common prefixes
        business_name = re.sub(r'^(The\s+)', '', business_name, flags=re.IGNORECASE)
        
        # Standardize business entity suffixes
        suffixes = {
            r'\b(inc|incorporated)\b': 'Inc.',
            r'\b(llc|l\.l\.c\.)\b': 'LLC',
            r'\b(corp|corporation)\b': 'Corp.',
            r'\b(ltd|limited)\b': 'Ltd.',
            r'\b(co|company)\b': 'Company'
        }
        
        for pattern, replacement in suffixes.items():
            business_name = re.sub(pattern, replacement, business_name, flags=re.IGNORECASE)
        
        return business_name.strip()
    
    def _clean_location(self, location: str) -> str:
        """Clean and standardize location information."""
        if not location:
            return ""
        
        # Remove extra whitespace
        location = re.sub(r'\s+', ' ', location.strip())
        
        # Title case
        location = location.title()
        
        # Standardize state abbreviations
        state_abbrevs = {
            'Texas': 'TX', 'California': 'CA', 'Florida': 'FL',
            'New York': 'NY', 'Illinois': 'IL', 'Pennsylvania': 'PA'
            # Add more as needed
        }
        
        for state, abbrev in state_abbrevs.items():
            location = re.sub(rf'\b{state}\b', abbrev, location, flags=re.IGNORECASE)
        
        return location.strip()
    
    def _clean_phone(self, phone: str) -> str:
        """Clean and standardize phone numbers."""
        if not phone:
            return ""
        
        # Remove all non-digit characters
        digits = re.sub(r'\D', '', phone)
        
        # Format as (XXX) XXX-XXXX if 10 digits
        if len(digits) == 10:
            return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
        elif len(digits) == 11 and digits[0] == '1':
            return f"({digits[1:4]}) {digits[4:7]}-{digits[7:]}"
        
        return phone  # Return original if can't format
    
    def _clean_email(self, email: str) -> str:
        """Clean and validate email addresses."""
        if not email:
            return ""
        
        email = email.strip().lower()
        
        # Basic email validation
        if re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
            return email
        
        return ""  # Return empty if invalid
    
    def _clean_address(self, address: str) -> str:
        """Clean and standardize addresses."""
        if not address:
            return ""
        
        # Remove extra whitespace
        address = re.sub(r'\s+', ' ', address.strip())
        
        # Title case
        address = address.title()
        
        # Standardize common abbreviations
        abbrevs = {
            r'\bStreet\b': 'St',
            r'\bAvenue\b': 'Ave',
            r'\bBoulevard\b': 'Blvd',
            r'\bRoad\b': 'Rd',
            r'\bDrive\b': 'Dr',
            r'\bLane\b': 'Ln'
        }
        
        for pattern, replacement in abbrevs.items():
            address = re.sub(pattern, replacement, address, flags=re.IGNORECASE)
        
        return address.strip()
    
    def _validate_results(self, results: List[ScrapingResult]) -> List[ScrapingResult]:
        """Validate and filter results."""
        valid_results = []
        
        for result in results:
            # Must have either owner name or business name
            if not result.owner_name and not result.business_name:
                continue
            
            # Owner name validation
            if result.owner_name:
                # Must be at least 2 characters and look like a name
                if len(result.owner_name) < 2:
                    continue
                if not re.match(r'^[A-Za-z\s\'-\.]+$', result.owner_name):
                    continue
            
            # Business name validation
            if result.business_name:
                # Must be at least 2 characters
                if len(result.business_name) < 2:
                    continue
            
            valid_results.append(result)
        
        self.logger.info(f"Validation: {len(valid_results)}/{len(results)} results passed")
        return valid_results
    
    def _deduplicate_results(self, results: List[ScrapingResult]) -> List[ScrapingResult]:
        """Remove duplicate results based on similarity."""
        if not results:
            return results
        
        self.logger.info(f"Deduplicating {len(results)} results")
        
        # Group results by similarity
        groups = self._group_similar_results(results)
        
        # Select best result from each group
        deduplicated = []
        for group in groups:
            best_result = self._select_best_result(group)
            deduplicated.append(best_result)
        
        self.logger.info(f"Deduplication: {len(deduplicated)} unique results from {len(results)} total")
        return deduplicated
    
    def _group_similar_results(self, results: List[ScrapingResult]) -> List[List[ScrapingResult]]:
        """Group similar results together."""
        groups = []
        used_indices = set()
        
        for i, result1 in enumerate(results):
            if i in used_indices:
                continue
            
            group = [result1]
            used_indices.add(i)
            
            for j, result2 in enumerate(results[i+1:], i+1):
                if j in used_indices:
                    continue
                
                if self._are_similar(result1, result2):
                    group.append(result2)
                    used_indices.add(j)
            
            groups.append(group)
        
        return groups
    
    def _are_similar(self, result1: ScrapingResult, result2: ScrapingResult) -> bool:
        """Check if two results are similar enough to be considered duplicates."""
        similarities = []
        
        # Compare owner names
        if result1.owner_name and result2.owner_name:
            name_sim = self._string_similarity(result1.owner_name, result2.owner_name)
            similarities.append(name_sim)
        
        # Compare business names
        if result1.business_name and result2.business_name:
            business_sim = self._string_similarity(result1.business_name, result2.business_name)
            similarities.append(business_sim)
        
        # If no comparable fields, not similar
        if not similarities:
            return False
        
        # Average similarity must exceed threshold
        avg_similarity = sum(similarities) / len(similarities)
        return avg_similarity >= self.similarity_threshold
    
    def _string_similarity(self, str1: str, str2: str) -> float:
        """Calculate similarity between two strings."""
        if not str1 or not str2:
            return 0.0
        
        # Normalize strings
        str1 = str1.lower().strip()
        str2 = str2.lower().strip()
        
        # Exact match
        if str1 == str2:
            return 1.0
        
        # Use SequenceMatcher for similarity
        return SequenceMatcher(None, str1, str2).ratio()
    
    def _select_best_result(self, group: List[ScrapingResult]) -> ScrapingResult:
        """Select the best result from a group of similar results."""
        if len(group) == 1:
            return group[0]
        
        # Score results based on completeness
        scored_results = []
        for result in group:
            score = self._calculate_completeness_score(result)
            scored_results.append((score, result))
        
        # Sort by score (highest first)
        scored_results.sort(key=lambda x: x[0], reverse=True)
        
        # Merge information from all results into the best one
        best_result = scored_results[0][1]
        merged_result = self._merge_results(group, best_result)
        
        return merged_result
    
    def _calculate_completeness_score(self, result: ScrapingResult) -> int:
        """Calculate completeness score for a result."""
        score = 0
        
        if result.owner_name:
            score += 3
        if result.business_name:
            score += 3
        if result.phone:
            score += 2
        if result.email:
            score += 2
        if result.address:
            score += 1
        if result.business_type:
            score += 1
        if result.location:
            score += 1
        
        return score
    
    def _merge_results(self, group: List[ScrapingResult], base_result: ScrapingResult) -> ScrapingResult:
        """Merge information from multiple results into one."""
        merged = base_result
        
        # Collect all non-empty values for each field
        for result in group:
            if not merged.owner_name and result.owner_name:
                merged.owner_name = result.owner_name
            if not merged.business_name and result.business_name:
                merged.business_name = result.business_name
            if not merged.phone and result.phone:
                merged.phone = result.phone
            if not merged.email and result.email:
                merged.email = result.email
            if not merged.address and result.address:
                merged.address = result.address
            if not merged.business_type and result.business_type:
                merged.business_type = result.business_type
            if not merged.location and result.location:
                merged.location = result.location
        
        # Merge raw data
        merged_raw_data = {}
        for result in group:
            if result.raw_data:
                merged_raw_data.update(result.raw_data)
        merged.raw_data = merged_raw_data
        
        return merged
    
    def _enrich_results(self, results: List[ScrapingResult]) -> List[ScrapingResult]:
        """Enrich results with additional information."""
        for result in results:
            # Add confidence score
            result.raw_data['confidence_score'] = self._calculate_confidence_score(result)
            
            # Add data quality indicators
            result.raw_data['data_quality'] = self._assess_data_quality(result)
        
        return results
    
    def _calculate_confidence_score(self, result: ScrapingResult) -> float:
        """Calculate confidence score for a result."""
        score = 0.0
        max_score = 0.0
        
        # Owner name confidence
        if result.owner_name:
            max_score += 0.4
            if len(result.owner_name.split()) >= 2:  # First and last name
                score += 0.4
            else:
                score += 0.2
        
        # Business name confidence
        if result.business_name:
            max_score += 0.3
            score += 0.3
        
        # Contact info confidence
        if result.phone:
            max_score += 0.15
            score += 0.15
        
        if result.email:
            max_score += 0.15
            score += 0.15
        
        return score / max_score if max_score > 0 else 0.0
    
    def _assess_data_quality(self, result: ScrapingResult) -> str:
        """Assess overall data quality."""
        completeness = self._calculate_completeness_score(result)
        
        if completeness >= 8:
            return "high"
        elif completeness >= 5:
            return "medium"
        else:
            return "low"
