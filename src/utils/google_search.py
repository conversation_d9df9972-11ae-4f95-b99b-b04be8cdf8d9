"""
Google search utilities for site-specific business owner searches.
Implements the exact search patterns required by the client.
"""

import requests
import time
import random
from typing import List, Dict, Optional
from urllib.parse import quote_plus
from bs4 import BeautifulSoup
import logging


class GoogleSearchEngine:
    """Google search engine for site-specific business owner queries."""
    
    def __init__(self, anti_bot_config: Dict):
        self.logger = logging.getLogger(__name__)
        self.anti_bot = anti_bot_config
        self.base_url = "https://www.google.com/search"
        
    def search_site_for_owners(self, site: str, business_type: str, location: str, 
                              max_results: int = 20) -> List[Dict]:
        """
        Execute Google search with site: operator for business owners.
        
        Search pattern: site:bbb.org "Owner" "lawn care" "Dallas"
        """
        # Build the exact search query as specified by client
        query = f'site:{site} "Owner" "{business_type}" "{location}"'
        
        self.logger.info(f"Executing Google search: {query}")
        
        search_url = f"{self.base_url}?q={quote_plus(query)}&num={max_results}"
        
        try:
            # Use anti-bot protection for Google search
            headers = self._get_google_headers()
            
            response = requests.get(search_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            # Parse search results
            results = self._parse_google_results(response.text, site)
            
            self.logger.info(f"Found {len(results)} Google search results for {site}")
            
            # Add delay to avoid rate limiting
            time.sleep(random.uniform(2, 5))
            
            return results
            
        except Exception as e:
            self.logger.error(f"Google search failed for {site}: {e}")
            return []
    
    def _get_google_headers(self) -> Dict[str, str]:
        """Get headers optimized for Google search."""
        return {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }
    
    def _parse_google_results(self, html: str, target_site: str) -> List[Dict]:
        """Parse Google search results HTML."""
        results = []
        soup = BeautifulSoup(html, 'lxml')
        
        # Google search result selectors
        result_selectors = [
            'div.g',  # Standard result container
            'div[data-ved]',  # Alternative result container
            '.rc'  # Classic result container
        ]
        
        for selector in result_selectors:
            search_results = soup.select(selector)
            
            for result_div in search_results:
                # Extract link
                link_elem = result_div.select_one('h3 a, a h3, .yuRUbf a')
                if not link_elem:
                    continue
                
                url = link_elem.get('href')
                if not url or target_site not in url:
                    continue
                
                # Extract title
                title_elem = result_div.select_one('h3')
                title = title_elem.get_text(strip=True) if title_elem else ""
                
                # Extract snippet
                snippet_elem = result_div.select_one('.VwiC3b, .s3v9rd, .st, .IsZvec')
                snippet = snippet_elem.get_text(strip=True) if snippet_elem else ""
                
                results.append({
                    'url': url,
                    'title': title,
                    'snippet': snippet,
                    'source_site': target_site
                })
        
        return results
    
    def search_all_sites(self, business_type: str, location: str) -> Dict[str, List[Dict]]:
        """
        Search all target sites using Google site: operator.
        
        Returns results grouped by site.
        """
        target_sites = [
            'bbb.org',
            'manta.com', 
            'linkedin.com'
        ]
        
        all_results = {}
        
        for site in target_sites:
            self.logger.info(f"Searching {site} via Google...")
            
            site_results = self.search_site_for_owners(site, business_type, location)
            all_results[site] = site_results
            
            # Delay between site searches
            time.sleep(random.uniform(3, 7))
        
        return all_results
